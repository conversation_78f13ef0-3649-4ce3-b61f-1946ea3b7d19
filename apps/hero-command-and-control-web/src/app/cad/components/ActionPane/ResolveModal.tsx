"use client";
import CloseIcon from "@mui/icons-material/Close";
import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Typography,
} from "@mui/material";
import { Asset } from "proto/hero/assets/v2/assets_pb";
import { Order, OrderStatus } from "proto/hero/orders/v2/orders_pb";
import React, { useEffect, useState } from "react";

type ResolveModalProps = {
  open: boolean;
  onClose: () => void;
  onNext: (disposition: string, primaryResponderId?: string) => void;
  isAllAssistMemberOrdersCompleted: boolean;
  hasExistingCase?: boolean;
  availableResponders?: Asset[];
  ordersForSituation?: Order[];
};

const ResolveModal: React.FC<ResolveModalProps> = ({
  open,
  onClose,
  onNext,
  isAllAssistMemberOrdersCompleted,
  hasExistingCase = true,
  availableResponders = [],
  ordersForSituation = [],
}) => {
  const [disposition, setDisposition] = useState("");
  const [primaryResponderId, setPrimaryResponderId] = useState<string | null>(null);

  // Find the responder with the earliest uncancelled assist member order
  const getDefaultPrimaryResponder = () => {
    const uncancelledOrders = ordersForSituation.filter(
      (order) =>
        order.status !== OrderStatus.CANCELLED &&
        order.status !== OrderStatus.REJECTED
    );

    if (uncancelledOrders.length === 0) return null;

    // Sort by create time (earliest first)
    const sortedOrders = uncancelledOrders.sort((a, b) => {
      const aTime = a.createTime ? new Date(a.createTime).getTime() : 0;
      const bTime = b.createTime ? new Date(b.createTime).getTime() : 0;
      return aTime - bTime;
    });

    return sortedOrders[0]?.assetId || null;
  };

  // Get responders with uncancelled orders
  const getRespondersWithUncancelledOrders = () => {
    const uncancelledOrderAssetIds = ordersForSituation
      .filter(order => order.status !== OrderStatus.CANCELLED && order.status !== OrderStatus.REJECTED)
      .map(order => order.assetId);

    return availableResponders.filter(responder => uncancelledOrderAssetIds.includes(responder.id));
  };

  // Set default primary responder when modal opens
  useEffect(() => {
    if (open && availableResponders.length > 0) {
      const defaultResponderId = getDefaultPrimaryResponder();
      const filteredResponders = getRespondersWithUncancelledOrders();
      if (defaultResponderId && filteredResponders.some(r => r.id === defaultResponderId)) {
        setPrimaryResponderId(defaultResponderId);
      }
    }
  }, [open, availableResponders, ordersForSituation]);

  const handleChange = (event: SelectChangeEvent) => {
    setDisposition(event.target.value);
  };

  const handleNext = () => {
    onNext(disposition, primaryResponderId || undefined);
  };

  const handleClose = () => {
    setDisposition("");
    setPrimaryResponderId(null);
    onClose();
  };

  const dispositionOptions = [
    { label: "Arrest Made", abbr: "ARR" },
    { label: "Cancelled", abbr: "CAN" },
    { label: "Medical Assist", abbr: "MED" },
    { label: "No Report Required", abbr: "NRR" },
    { label: "Report Taken", abbr: "RPT" },
    { label: "Unfounded Event", abbr: "UNF" },
  ];

  // Convert available responders to dropdown options
  const responderOptions = getRespondersWithUncancelledOrders().map((responder) => ({
    value: responder.id,
    label: responder.name || responder.id,
  }));

  return (
    <>
      <Dialog
        open={open}
        onClose={handleClose}
        fullWidth
        maxWidth="xs"
        PaperProps={{
          style: {
            backgroundColor: "#F8FAFB",
            padding: "16px",
            borderRadius: "8px",
          },
        }}
        BackdropProps={{
          style: {
            backdropFilter: "blur(4px)",
          },
        }}
        sx={{
          "& .MuiDialog-paper": {
            transform: "translateY(-20%)",
          },
        }}
      >
        <DialogTitle
          sx={{
            textAlign: "center",
            position: "relative",
            padding: "16px 16px 8px",
            fontSize: "14px",
            fontWeight: 600,
          }}
        >
          Resolve
          <IconButton
            onClick={onClose}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
              color: "black",
            }}
          >
            <CloseIcon fontSize="medium" />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ pt: "8px", pb: "10px" }}>
          {!isAllAssistMemberOrdersCompleted && (
            <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
              <WarningAmberIcon sx={{ color: "orange", mr: 1 }} />
              <Typography variant="body2" color="textSecondary">
                Not all assist member orders are completed!
              </Typography>
            </Box>
          )}

          {!hasExistingCase && (
            <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
              <WarningAmberIcon sx={{ color: "orange", mr: 1 }} />
              <Typography variant="body2" color="textSecondary">
                No case exists for this situation. A report will not be created upon resolution.
              </Typography>
            </Box>
          )}

          <FormControl fullWidth sx={{ mt: 2, mb: 2 }}>
            <InputLabel id="disposition-label" size="small">
              Select Disposition
            </InputLabel>
            <Select
              labelId="disposition-label"
              value={disposition}
              onChange={handleChange}
              label="Select Disposition"
              size="small"
              sx={{
                backgroundColor: "#ffffff",
                borderRadius: "8px",
              }}
            >
              {dispositionOptions.map((option) => (
                <MenuItem key={option.label} value={option.label}>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                      width: "100%",
                      color: "#35353E",
                      fontWeight: "bold",
                      fontSize: "14px",
                    }}
                  >
                    <span>{option.label}</span>
                    <Typography
                      variant="caption"
                      sx={{ color: "#9C9CA0", lineHeight: 1 }}
                    >
                      {option.abbr}
                    </Typography>
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {/* Primary Responder Dropdown */}
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel id="primary-responder-label" size="small">
              Primary Responder (Optional)
            </InputLabel>
            <Select
              labelId="primary-responder-label"
              value={primaryResponderId || ""}
              onChange={(event) => setPrimaryResponderId(event.target.value || null)}
              label="Primary Responder (Optional)"
              size="small"
              sx={{
                backgroundColor: "#ffffff",
                borderRadius: "8px",
              }}
            >
              {responderOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  <Typography
                    sx={{
                      color: "#35353E",
                      fontWeight: "bold",
                      fontSize: "14px",
                    }}
                  >
                    {option.label}
                  </Typography>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions sx={{ padding: "0 22px 16px" }}>
          <Button
            variant="contained"
            onClick={handleNext}
            disabled={!disposition}
            fullWidth
            size="small"
            sx={{
              height: "40px",
              padding: 0,
              backgroundColor: "#EAF2FF",
              border: "1px solid rgba(0,96,255,1)",
              color: "rgba(0,96,255,1)",
              "&.Mui-disabled": {
                backgroundColor: "#EAF2FF",
                border: "1px solid rgba(0,96,255,0.3)",
                color: "rgba(0,96,255,0.3)",
              },
              textTransform: "none",
              borderRadius: "8px",
            }}
          >
            Next
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ResolveModal;
