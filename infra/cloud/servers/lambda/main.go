package main

import (
	clients "common/clients/services"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	orgs "proto/hero/orgs/v1"
	"strings"

	"connectrpc.com/connect"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
)

type AuthResponse struct {
	Context struct {
		OrgID string `json:"org_id"`
	} `json:"context"`
	PrincipalID    string `json:"principalId"`
	PolicyDocument *struct {
		Version   string `json:"Version"`
		Statement []struct {
			Action   string   `json:"Action"`
			Effect   string   `json:"Effect"`
			Resource []string `json:"Resource"`
		} `json:"Statement"`
	} `json:"policyDocument,omitempty"`
}

// Perms service will handle fine grained permissions, so we allow all resources
var allowedResources = []string{
	"arn:aws:execute-api:us-west-2:816069150268:qghkb8ucte/prod/POST/*",
}

func generatePolicy(principalID string, effect string, resources []string, orgID int32, username string) events.APIGatewayCustomAuthorizerResponse {
	response := events.APIGatewayCustomAuthorizerResponse{
		PrincipalID: principalID,
		Context: map[string]interface{}{
			"org_id":   orgID,
			"username": username,
		},
	}

	if effect != "" && len(resources) > 0 {
		response.PolicyDocument = events.APIGatewayCustomAuthorizerPolicy{
			Version: "2012-10-17",
			Statement: []events.IAMPolicyStatement{
				{
					Action:   []string{"execute-api:Invoke"},
					Effect:   effect,
					Resource: resources,
				},
			},
		}
	}

	return response
}

func generateAllow(principalID string, resource []string, orgID int32, username string) events.APIGatewayCustomAuthorizerResponse {
	return generatePolicy(principalID, "Allow", resource, orgID, username)
}

func generateDeny(principalID string, resource []string, orgID int32, username string) events.APIGatewayCustomAuthorizerResponse {
	return generatePolicy(principalID, "Deny", resource, orgID, username)
}

func getSecret(secretARN string) (map[string]string, error) {
	port := os.Getenv("PARAMETERS_SECRETS_EXTENSION_HTTP_PORT")
	if port == "" {
		port = "2773"
	}

	url := fmt.Sprintf("http://localhost:%s/secretsmanager/get?secretId=%s", port, secretARN)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("error creating request: %v", err)
	}

	req.Header.Set("X-Aws-Parameters-Secrets-Token", os.Getenv("AWS_SESSION_TOKEN"))

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error making request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP error! status: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response: %v", err)
	}

	var secretData struct {
		SecretString string `json:"SecretString"`
	}
	if err := json.Unmarshal(body, &secretData); err != nil {
		return nil, fmt.Errorf("error unmarshaling response: %v", err)
	}

	var secret map[string]string
	if err := json.Unmarshal([]byte(secretData.SecretString), &secret); err != nil {
		return nil, fmt.Errorf("error unmarshaling secret: %v", err)
	}

	return secret, nil
}

func handleRequest(ctx context.Context, event events.APIGatewayCustomAuthorizerRequestTypeRequest) (events.APIGatewayCustomAuthorizerResponse, error) {
	// Get the Authorization header
	log.Printf("event: %v", event)
	authHeader := event.Headers["Authorization"]

	if !strings.HasPrefix(authHeader, "Basic ") {
		response := generateDeny("me", []string{event.MethodArn}, 0, "")
		return response, nil
	}

	// Extract and decode the credentials
	base64Credentials := strings.TrimPrefix(authHeader, "Basic ")
	credentials, err := base64.StdEncoding.DecodeString(base64Credentials)
	if err != nil {
		log.Printf("error decoding credentials: %v", err)
		return events.APIGatewayCustomAuthorizerResponse{}, fmt.Errorf("error decoding credentials: %v", err)
	}

	parts := strings.SplitN(string(credentials), ":", 2)
	if len(parts) != 2 {
		log.Printf("invalid credentials format")
		// log the credentials
		log.Printf("credentials: %v", string(credentials))
		return events.APIGatewayCustomAuthorizerResponse{}, fmt.Errorf("invalid credentials format")
	}

	orgsClient := clients.NewOrgsClient(os.Getenv("ORGS_SERVICE_URL"),
		clients.AuthHeaderSecretKeyInterceptor("OHBBOJHBLHJBLHJVLHJVLJTESTEVLJVL"))

	resp, err := orgsClient.ValidateOrgCreds(context.Background(), &connect.Request[orgs.ValidateOrgCredsRequest]{
		Msg: &orgs.ValidateOrgCredsRequest{
			Username: parts[0],
			Password: parts[1],
		},
	})
	if err != nil {
		log.Printf("error validating org credentials: %v", err)
		return events.APIGatewayCustomAuthorizerResponse{}, fmt.Errorf("error validating org credentials: %v", err)
	}

	log.Printf("org credentials validated: %v", resp.Msg.Valid)
	// log whole response
	log.Printf("org credentials response: %v", resp)

	if resp.Msg.Valid {
		orgID := resp.Msg.OrgApiUser.OrgId
		username := resp.Msg.OrgApiUser.Username

		// Validate credentials
		response := generateAllow("me", allowedResources, orgID, username)
		return response, nil
	}

	response := generateDeny("me", []string{event.MethodArn}, 0, "")
	return response, nil
}

func main() {
	lambda.Start(handleRequest)
}
